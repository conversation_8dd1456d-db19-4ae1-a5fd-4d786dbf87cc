# Piano di Migrazione: Da Viaggi Singoli a Viaggi Multi-Tappa

## 📋 Analisi dei Requisiti

### Situazione Attuale
- **Viaggio**: contiene una singola traccia GPX
- **Struttura**: titolo, descrizione, durata (giorni/notti), media, GPX singolo
- **Calcoli**: distanza basata su una sola traccia

### Nuova Struttura Richiesta
- **Viaggio**: contenitore di più tappe ordinate
- **Tappa**: unità base con numero ordinale, titolo, descrizione, tipo percorso, galleria immagini, traccia GPX
- **Calcoli automatici**: 
  - Distanza totale = somma distanze singole tappe
  - Numero giorni = numero tappe (1 tappa = 1 giorno)
  - Numero notti = numero tappe - 1

---

## 🎯 Obiettivi del Piano

1. **Preservare compatibilità**: viaggi esistenti devono continuare a funzionare
2. **Migrazione graduale**: possibilità di convertire viaggi esistenti
3. **Nuove funzionalità**: supporto completo per viaggi multi-tappa
4. **Mantenere prestazioni**: ottimizzazione query e calcoli

---

## 📊 Task List Prioritizzati

### 🔴 FASE 1: Fondamenta Database (Priorità ALTA)
**Tempo stimato: 3-4 giorni**

#### Task 1.1: Creazione nuovo modello Stage (Tappa)
- **Descrizione**: Creare il modello Prisma per le tappe
- **File coinvolti**: `prisma/schema.prisma`
- **Dettagli**:
  ```prisma
  model Stage {
    id                String     @id @default(cuid())
    tripId            String
    orderIndex        Int        // Numero ordinale della tappa
    title             String
    description       String?    @db.Text
    routeType         String?    // Tipo di percorso (testo libero)
    media             Json[]     @default([])
    gpxFile           Json?
    createdAt         DateTime   @default(now())
    updatedAt         DateTime   @updatedAt
    trip              Trip       @relation(fields: [tripId], references: [id], onDelete: Cascade)
    
    @@unique([tripId, orderIndex])
    @@map("stages")
  }
  ```

#### Task 1.2: Aggiornamento modello Trip
- **Descrizione**: Modificare Trip per supportare relazione con Stage
- **File coinvolti**: `prisma/schema.prisma`
- **Dettagli**:
  - Aggiungere relazione `stages Stage[]`
  - Mantenere campi esistenti per compatibilità backward
  - **Logica**: `stages.length > 0` = multi-tappa, `stages.length === 0` = singola tappa legacy

#### Task 1.3: Migrazione database
- **Descrizione**: Creare e applicare migration Prisma
- **Comando**: `npx prisma migrate dev --name add-stages-support`

### 🟡 FASE 2: Tipi TypeScript (Priorità ALTA)
**Tempo stimato: 1-2 giorni**

#### Task 2.1: Definizione tipi Stage
- **File coinvolti**: `src/types/trip.ts`
- **Dettagli**:
  ```typescript
  export interface Stage {
    id: string
    tripId: string
    orderIndex: number
    title: string
    description?: string
    routeType?: string
    media: MediaItem[]
    gpxFile: GpxFile | null
    createdAt: Date
    updatedAt: Date
  }
  
  export type StageCreationData = Omit<Stage, 'id' | 'tripId' | 'createdAt' | 'updatedAt'>
  export type StageUpdateData = Partial<StageCreationData>
  ```

#### Task 2.2: Aggiornamento tipi Trip
- **File coinvolti**: `src/types/trip.ts`
- **Dettagli**:
  - Aggiungere `stages?: Stage[]` a Trip
  - Aggiungere helper function `isMultiStageTrip(trip: Trip): boolean`
  - Mantenere campi legacy per compatibilità

### 🟡 FASE 3: Utility e Servizi Core (Priorità ALTA)
**Tempo stimato: 2-3 giorni**

#### Task 3.1: Aggiornamento trip-utils
- **File coinvolti**: `src/lib/trip-utils.ts`
- **Nuove funzioni**:
  ```typescript
  export const isMultiStageTrip = (trip: Trip): boolean => trip.stages && trip.stages.length > 0
  export const calculateTotalDistance = (trip: Trip): number
  export const calculateTripDuration = (trip: Trip): { days: number, nights: number }
  export const validateStageOrder = (stages: Stage[]): boolean
  export const reorderStages = (stages: Stage[], newOrder: number[]): Stage[]
  ```

#### Task 3.2: Servizi di gestione Stage
- **File da creare**: `src/lib/stage-utils.ts`
- **Funzioni**:
  ```typescript
  export const createStage = (data: StageCreationData): Promise<Stage>
  export const updateStage = (id: string, data: StageUpdateData): Promise<Stage>
  export const deleteStage = (id: string): Promise<void>
  export const getStagesByTripId = (tripId: string): Promise<Stage[]>
  ```

### 🟢 FASE 4: API Endpoints (Priorità MEDIA)
**Tempo stimato: 3-4 giorni**

#### Task 4.1: API per gestione Stage
- **File da creare**: `src/app/api/trips/[id]/stages/route.ts`
- **Endpoints**:
  - `GET /api/trips/[id]/stages` - Lista tappe di un viaggio
  - `POST /api/trips/[id]/stages` - Crea nuova tappa
  
#### Task 4.2: API per singola Stage
- **File da creare**: `src/app/api/trips/[id]/stages/[stageId]/route.ts`
- **Endpoints**:
  - `GET /api/trips/[id]/stages/[stageId]` - Dettaglio tappa
  - `PUT /api/trips/[id]/stages/[stageId]` - Aggiorna tappa
  - `DELETE /api/trips/[id]/stages/[stageId]` - Elimina tappa

#### Task 4.3: Aggiornamento API Trip esistenti
- **File coinvolti**: `src/app/api/trips/[id]/route.ts`, `src/app/api/trips/route.ts`
- **Modifiche**:
  - Includere stages nelle response quando `stages.length > 0`
  - Aggiornare calcoli distanza totale usando `isMultiStageTrip()`
  - Gestire backward compatibility

### 🟢 FASE 5: Componenti UI (Priorità MEDIA)
**Tempo stimato: 4-5 giorni**

#### Task 5.1: Componente StageCard
- **File da creare**: `src/components/stages/StageCard.tsx`
- **Features**: visualizzazione singola tappa con media, GPX preview, azioni

#### Task 5.2: Componente StageList
- **File da creare**: `src/components/stages/StageList.tsx`
- **Features**: lista ordinabile delle tappe, drag & drop per riordinamento

#### Task 5.3: Componente StageForm
- **File da creare**: `src/components/stages/StageForm.tsx`
- **Features**: form per creazione/modifica tappa

#### Task 5.4: Aggiornamento TripDetailPage
- **File coinvolti**: `src/app/trips/[slug]/page.tsx`
- **Modifiche**: gestire visualizzazione usando `isMultiStageTrip(trip)`

### 🔵 FASE 6: Hook e State Management (Priorità MEDIA)
**Tempo stimato: 2-3 giorni**

#### Task 6.1: Hook useStages
- **File da creare**: `src/hooks/useStages.ts`
- **Features**: gestione CRUD tappe, riordinamento, validazione

#### Task 6.2: Aggiornamento useTripForm
- **File coinvolti**: `src/hooks/useTripForm.ts`
- **Modifiche**: supporto per switch single/multi stage, gestione array tappe

### 🔵 FASE 7: Migrazione Dati (Priorità BASSA)
**Tempo stimato: 1-2 giorni**

#### Task 7.1: Script di migrazione viaggi esistenti
- **File da creare**: `src/scripts/migrate-existing-trips.ts`
- **Logica**:
  - Identificare viaggi con singola traccia GPX
  - Creare una tappa equivalente per ogni viaggio
  - Mantenere tutti i dati esistenti

#### Task 7.2: Comando di rollback
- **File da creare**: `src/scripts/rollback-migration.ts`
- **Sicurezza**: possibilità di tornare indietro se necessario

### 🟣 FASE 8: Testing (Priorità MEDIA)
**Tempo stimato: 3-4 giorni**

#### Task 8.1: Test unitari Stage utilities
- **File da creare**: `src/tests/unit/lib/stageUtils.test.ts`

#### Task 8.2: Test integrazione API Stages
- **File da creare**: `src/tests/integration/stages-api.test.ts`

#### Task 8.3: Test componenti UI
- **File da creare**: `src/tests/unit/components/stages/StageCard.test.tsx`

#### Task 8.4: Test migrazione dati
- **File da creare**: `src/tests/integration/data-migration.test.ts`

---

## 🚀 Timeline Esecuzione

### Sprint 1 (Settimana 1)
- **FASE 1**: Database foundations
- **FASE 2**: TypeScript types
- **Obiettivo**: Struttura dati pronta

### Sprint 2 (Settimana 2)
- **FASE 3**: Core utilities e servizi
- **FASE 4**: API endpoints
- **Obiettivo**: Backend funzionante

### Sprint 3 (Settimana 3)
- **FASE 5**: Componenti UI
- **FASE 6**: Hook e state management
- **Obiettivo**: Frontend completo

### Sprint 4 (Settimana 4)
- **FASE 7**: Migrazione dati esistenti
- **FASE 8**: Testing completo
- **Obiettivo**: Sistema ready per produzione

---

## ⚠️ Considerazioni Tecniche

### Compatibilità Backward
- I viaggi esistenti rimangono funzionanti (array `stages` vuoto)
- Logica: `stages.length > 0` = multi-tappa, `stages.length === 0` = legacy
- API response adattive basate sulla presenza di stages

### Performance
- Lazy loading delle tappe quando necessario
- Batch operations per operazioni multiple
- Caching dei calcoli di distanza totale

### Sicurezza
- Validazione ordinamento tappe
- Controllo permessi per modifica ordine
- Sanitizzazione input testo libero

### UX/UI
- Indicatori visivi per viaggi multi-tappa
- Transizioni smooth tra visualizzazioni
- Feedback immediato per operazioni di riordinamento

---

## 🎯 Criteri di Accettazione Globali

1. ✅ Viaggi esistenti continuano a funzionare (stages array vuoto)
2. ✅ Nuovi viaggi possono essere creati con più tappe (stages array popolato)
3. ✅ Calcolo automatico distanza totale e durata basato su `isMultiStageTrip()`
4. ✅ Interface intuitiva per riordinamento tappe
5. ✅ Performance mantenute rispetto a situazione attuale
6. ✅ Test coverage > 80% per nuove funzionalità
7. ✅ Logica chiara senza campi ridondanti

---

## 📝 Note per Implementazione

- **Testing First**: implementare test prima dell'implementazione
- **Atomic Changes**: ogni task deve essere deployabile separatamente  
- **Documentation**: aggiornare documentazione API per ogni endpoint
- **Code Review**: review obbligatorio per modifiche al database schema
- **Backup Strategy**: backup completo prima di ogni migrazione dati